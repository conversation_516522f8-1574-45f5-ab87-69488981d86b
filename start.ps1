$PYTHON_PATH = "$(Get-Location)\py311\"

$env:PYTHONHOME = ""
$env:PYTHONPATH = ""
$env:PYTHONEXECUTABLE = "$PYTHON_PATH\python.exe"
$env:PYTHONWEXECUTABLE = "$PYTHON_PATH\pythonw.exe"
$env:PYTHON_EXECUTABLE = "$PYTHON_PATH\python.exe"
$env:PYTHONW_EXECUTABLE = "$PYTHON_PATH\pythonw.exe"
$env:PYTHON_BIN_PATH = $env:PYTHON_EXECUTABLE
$env:PYTHON_LIB_PATH = "$PYTHON_PATH\Lib\site-packages"
$env:CU_PATH = "$PYTHON_PATH\Lib\site-packages\torch\lib"
$env:cuda_PATH = "$PYTHON_PATH\Library\bin"
$env:FFMPEG_PATH = "$(Get-Location)\py311\ffmpeg\bin"
$env:PATH = "$PYTHON_PATH;$PYTHON_PATH\Scripts;$env:FFMPEG_PATH;$env:CU_PATH;$env:cuda_PATH;$env:PATH"
$env:HF_ENDPOINT = "https://hf-mirror.com"
$env:HF_HOME = "$(Get-Location)\checkpoints"
$env:TRANSFORMERS_CACHE = "$(Get-Location)\checkpoints"
$env:XFORMERS_FORCE_DISABLE_TRITON = "1"

& $env:PYTHON_EXECUTABLE app.py