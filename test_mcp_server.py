#!/usr/bin/env python3
"""
BEN2 MCP Server Test Script
Tests the MCP server functionality without requiring a full MCP client
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add current directory to path to import mcp_server
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mcp_server():
    """Test the MCP server functionality"""
    print("🧪 Testing BEN2 MCP Server")
    print("=" * 50)
    
    try:
        # Import the MCP server functions
        from mcp_server import initialize_model, get_model_info, remove_background_image
        
        # Test 1: Initialize model
        print("1️⃣ Testing model initialization...")
        if initialize_model():
            print("✅ Model initialized successfully")
        else:
            print("❌ Model initialization failed")
            return False
        
        # Test 2: Get model info
        print("\n2️⃣ Testing get_model_info...")
        info = get_model_info()
        print(f"✅ Model info retrieved:")
        print(json.dumps(info, indent=2))
        
        # Test 3: Test image processing (if test image exists)
        test_image_path = "image.jpg"
        if os.path.exists(test_image_path):
            print(f"\n3️⃣ Testing image processing with {test_image_path}...")
            result = remove_background_image(
                image_input=test_image_path,
                input_type="path",
                output_format="path"
            )
            
            if result.get("success"):
                print("✅ Image processing successful")
                if "image_path" in result:
                    print(f"📁 Output saved to: {result['image_path']}")
            else:
                print(f"❌ Image processing failed: {result.get('error')}")
        else:
            print(f"\n3️⃣ Skipping image test - {test_image_path} not found")
        
        print("\n🎉 All tests completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

async def test_tools_directly():
    """Test individual tool functions"""
    print("\n🔧 Testing individual tools...")
    
    try:
        from mcp_server import mcp
        
        # Get available tools
        tools = []
        for name, func in mcp._tools.items():
            tools.append({
                "name": name,
                "description": func.__doc__ or "No description available"
            })
        
        print(f"📋 Available tools ({len(tools)}):")
        for tool in tools:
            print(f"  - {tool['name']}: {tool['description'].split('.')[0]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool enumeration failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 BEN2 MCP Server Test Suite")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("ben_base.py"):
        print("❌ Error: Please run this script from the BEN2 project directory")
        print("Current directory:", os.getcwd())
        sys.exit(1)
    
    # Run tests
    try:
        # Test basic functionality
        success = asyncio.run(test_mcp_server())
        
        # Test tools enumeration
        asyncio.run(test_tools_directly())
        
        if success:
            print("\n✅ All tests passed! MCP server is ready to use.")
            print("\nNext steps:")
            print("1. Start the MCP server: python start_mcp_server.py")
            print("2. Configure your MCP client (e.g., Claude Desktop)")
            print("3. Start using BEN2 through your AI assistant!")
        else:
            print("\n❌ Some tests failed. Please check the errors above.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
