---
title: BEN2
emoji: 🚀
colorFrom: purple
colorTo: gray
sdk: gradio
sdk_version: 5.13.1
app_file: app.py
pinned: false
---

# BEN2 - Background Removal Tool

BEN2 是一个强大的背景移除工具，支持图像和视频处理。

## 功能特性

- 🖼️ **图像背景移除**: 高质量的单张图像背景移除
- 🎥 **视频背景移除**: 支持视频文件的背景移除
- 🌐 **Web界面**: 基于 Gradio 的用户友好界面
- 🤖 **MCP支持**: 支持 Model Context Protocol，可与AI助手集成

## 使用方式

### 1. Web界面使用

```bash
python app.py
```

然后在浏览器中访问显示的地址。

### 2. MCP服务器模式（AI助手集成）

BEN2 现在支持 Model Context Protocol (MCP)，可以让 Claude 等AI助手直接调用背景移除功能。

#### 安装依赖

```bash
pip install -r requirements.txt
```

#### 启动MCP服务器

```bash
# 使用启动脚本（推荐）
python start_mcp_server.py

# 或直接启动
python mcp_server.py

# Windows用户可以使用
start_mcp_server.bat
```

#### 配置Claude Desktop

详细配置说明请参考 [README_MCP.md](README_MCP.md)

## 文件说明

- `app.py` - Gradio Web应用主文件
- `ben_base.py` - BEN2模型核心实现
- `mcp_server.py` - MCP服务器实现
- `start_mcp_server.py` - MCP服务器启动脚本
- `test_mcp_server.py` - MCP服务器测试脚本
- `README_MCP.md` - MCP使用详细说明

## 系统要求

- Python 3.8+
- PyTorch 1.9.0+
- CUDA支持（可选，用于GPU加速）

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference
