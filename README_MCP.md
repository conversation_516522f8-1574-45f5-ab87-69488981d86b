# BEN2 MCP Server 使用指南

## 概述

BEN2 MCP Server 是基于 Model Context Protocol (MCP) 的背景移除服务，允许大语言模型（如 Claude）直接调用 BEN2 的背景移除功能。

## 功能特性

- 🖼️ **图像背景移除**: 支持单张图像的背景移除
- 🎥 **视频背景移除**: 支持视频文件的背景移除（限制前100帧）
- 📁 **多种输入格式**: 支持文件路径和 base64 编码输入
- 🔄 **灵活输出**: 支持 base64 编码和文件路径输出
- 🤖 **MCP 兼容**: 完全兼容 Model Context Protocol 标准

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务器

### 方法 1: 使用启动脚本（推荐）

```bash
python start_mcp_server.py
```

### 方法 2: 直接启动

```bash
python mcp_server.py
```

## 配置 Claude Desktop

1. 找到 Claude Desktop 的配置文件：
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

2. 添加 BEN2 MCP 服务器配置：

```json
{
  "mcpServers": {
    "ben2-background-removal": {
      "command": "python",
      "args": ["E:/aibox/ai/BEN2/BEN2/mcp_server.py"],
      "cwd": "E:/aibox/ai/BEN2/BEN2"
    }
  }
}
```

**注意**: 请将路径替换为您的实际项目路径。

## 可用工具

### 1. remove_background_image

移除图像背景。

**参数**:
- `image_input` (string): 图像输入 - 文件路径或 base64 编码
- `input_type` (string, 可选): 输入类型 - "path" 或 "base64"，默认 "path"
- `output_format` (string, 可选): 输出格式 - "base64"、"path" 或 "both"，默认 "base64"

**示例**:
```
请帮我移除这张图片的背景：/path/to/image.jpg
```

### 2. remove_background_video

移除视频背景。

**参数**:
- `video_path` (string): 输入视频文件路径
- `output_path` (string, 可选): 输出视频路径，默认 "./foreground.mp4"

**示例**:
```
请处理这个视频文件，移除背景：/path/to/video.mp4
```

### 3. get_model_info

获取模型和服务器信息。

**示例**:
```
请告诉我 BEN2 模型的信息
```

## 使用示例

### 图像处理示例

```
用户: 请帮我移除这张图片的背景：image.jpg

Claude 会调用: remove_background_image(image_input="image.jpg", input_type="path", output_format="base64")

返回: 处理后的图像（base64 格式）
```

### 视频处理示例

```
用户: 请处理这个视频，移除背景：video.mp4

Claude 会调用: remove_background_video(video_path="video.mp4")

返回: 处理后的视频文件路径
```

## 支持的文件格式

### 图像格式
- PNG
- JPEG/JPG
- BMP
- TIFF

### 视频格式
- MP4
- AVI
- MOV
- MKV

## 注意事项

1. **模型文件**: 确保 `checkpoints/BEN2_Base.pth` 文件存在
2. **GPU 支持**: 如果有 CUDA GPU，会自动使用以提高处理速度
3. **视频限制**: 视频处理限制为前100帧以避免超时
4. **输出目录**: 处理后的文件会保存在 `output_images` 目录中

## 故障排除

### 常见问题

1. **模型文件未找到**
   ```
   错误: Model file not found: checkpoints/BEN2_Base.pth
   解决: 确保模型文件在正确位置
   ```

2. **依赖包缺失**
   ```
   错误: Missing required packages
   解决: 运行 pip install -r requirements.txt
   ```

3. **内存不足**
   ```
   解决: 尝试处理较小的图像或视频文件
   ```

### 日志查看

服务器运行时会输出详细日志，包括：
- 模型初始化状态
- 处理请求信息
- 错误信息

## 开发信息

- **MCP 版本**: 1.0.0+
- **Python 版本**: 3.8+
- **PyTorch 版本**: 1.9.0+

## 许可证

请参考项目主 README 文件中的许可证信息。
