#!/usr/bin/env python3
"""
BEN2 MCP Installation Script
Helps users set up the MCP environment for BEN2
"""

import os
import sys
import subprocess
import json
import platform
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_dependencies():
    """Install required Python packages"""
    print("🔧 Installing dependencies...")
    
    # Check if pip is available
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("❌ pip is not available. Please install pip first.")
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
        return False
    
    return True

def check_model_files():
    """Check if model files exist"""
    model_path = "checkpoints/BEN2_Base.pth"
    if os.path.exists(model_path):
        print(f"✅ Model file found: {model_path}")
        return True
    else:
        print(f"⚠️  Model file not found: {model_path}")
        print("Please ensure the model file is downloaded and placed in the correct location.")
        return False

def get_claude_config_path():
    """Get the Claude Desktop configuration file path"""
    system = platform.system()
    
    if system == "Windows":
        config_path = Path(os.environ.get("APPDATA", "")) / "Claude" / "claude_desktop_config.json"
    elif system == "Darwin":  # macOS
        config_path = Path.home() / "Library" / "Application Support" / "Claude" / "claude_desktop_config.json"
    else:  # Linux and others
        config_path = Path.home() / ".config" / "Claude" / "claude_desktop_config.json"
    
    return config_path

def setup_claude_config():
    """Set up Claude Desktop configuration"""
    print("🔧 Setting up Claude Desktop configuration...")
    
    config_path = get_claude_config_path()
    current_dir = os.path.abspath(".")
    
    # Create the configuration
    mcp_config = {
        "mcpServers": {
            "ben2-background-removal": {
                "command": "python",
                "args": [os.path.join(current_dir, "mcp_server.py")],
                "cwd": current_dir
            }
        }
    }
    
    print(f"📁 Claude config path: {config_path}")
    print(f"📁 BEN2 project path: {current_dir}")
    
    # Check if config directory exists
    config_dir = config_path.parent
    if not config_dir.exists():
        print(f"⚠️  Claude config directory doesn't exist: {config_dir}")
        print("Please install Claude Desktop first.")
        return False
    
    # Read existing config if it exists
    existing_config = {}
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
        except json.JSONDecodeError:
            print("⚠️  Existing Claude config is invalid JSON. Creating new config.")
            existing_config = {}
    
    # Merge configurations
    if "mcpServers" not in existing_config:
        existing_config["mcpServers"] = {}
    
    existing_config["mcpServers"]["ben2-background-removal"] = mcp_config["mcpServers"]["ben2-background-removal"]
    
    # Write the configuration
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(existing_config, f, indent=2)
        print(f"✅ Claude Desktop configuration updated")
        print(f"📝 Config saved to: {config_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to write Claude config: {e}")
        return False

def test_installation():
    """Test the installation"""
    print("🧪 Testing installation...")
    
    try:
        # Try to import required modules
        import torch
        import gradio
        from PIL import Image
        print("✅ Core dependencies imported successfully")
        
        # Try to import MCP modules
        try:
            import mcp
            from mcp.server.fastmcp import FastMCP
            print("✅ MCP dependencies imported successfully")
        except ImportError as e:
            print(f"❌ MCP import failed: {e}")
            return False
        
        # Test model initialization (without actually loading)
        if os.path.exists("ben_base.py"):
            print("✅ BEN2 model file found")
        else:
            print("❌ BEN2 model file not found")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 BEN2 MCP Installation Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("app.py") or not os.path.exists("ben_base.py"):
        print("❌ Error: Please run this script from the BEN2 project directory")
        print(f"Current directory: {os.getcwd()}")
        sys.exit(1)
    
    success = True
    
    # Step 1: Install dependencies
    if not install_dependencies():
        success = False
    
    # Step 2: Check model files
    if not check_model_files():
        print("⚠️  Warning: Model file missing. MCP server may not work properly.")
    
    # Step 3: Test installation
    if not test_installation():
        success = False
    
    # Step 4: Setup Claude configuration (optional)
    print("\n" + "=" * 50)
    setup_claude = input("🤖 Do you want to set up Claude Desktop configuration? (y/n): ").lower().strip()
    if setup_claude in ['y', 'yes']:
        if not setup_claude_config():
            print("⚠️  Claude configuration failed, but you can set it up manually later.")
    
    # Final status
    print("\n" + "=" * 50)
    if success:
        print("🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Test the MCP server: python test_mcp_server.py")
        print("2. Start the MCP server: python start_mcp_server.py")
        print("3. Restart Claude Desktop if you configured it")
        print("4. Start using BEN2 through your AI assistant!")
        print("\nFor detailed usage instructions, see README_MCP.md")
    else:
        print("❌ Installation completed with errors.")
        print("Please check the error messages above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
