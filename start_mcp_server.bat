@echo off
echo Starting BEN2 MCP Server...
echo ================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "mcp_server.py" (
    echo Error: mcp_server.py not found
    echo Please run this script from the BEN2 project directory
    pause
    exit /b 1
)

if not exist "checkpoints\BEN2_Base.pth" (
    echo Error: Model file not found at checkpoints\BEN2_Base.pth
    echo Please ensure the model file is in the correct location
    pause
    exit /b 1
)

REM Start the MCP server
echo Starting MCP server...
python start_mcp_server.py

pause
