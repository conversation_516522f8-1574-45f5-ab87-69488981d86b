# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a background removal tool called BEN2 that uses a Swin Transformer-based neural network to separate foreground objects from their backgrounds in images and videos. The model is implemented in PyTorch and uses Gradio for the web interface.

## Key Files

- `app.py`: Main application file with Gradio interface for image and video processing
- `ben_base.py`: Core model implementation with Swin Transformer architecture
- `checkpoints/BEN2_Base.pth`: Pre-trained model weights (Git LFS)
- `requirements.txt`: Python dependencies

## Architecture

The model uses a Swin Transformer backbone with a multi-scale feature extraction and fusion approach:
1. Input images are processed at multiple scales (local patches and global view)
2. Features are extracted through a Swin Transformer encoder
3. Multi-field cross-attention mechanism fuses local and global features
4. Multi-scale refinement modules progressively improve the segmentation
5. Final mask is generated and applied to the original image

## Common Development Tasks

### Running the Application
```bash
python app.py
```

### Installing Dependencies
```bash
pip install -r requirements.txt
```

### Key Dependencies
- PyTorch >= 1.9.0
- Swin Transformer (timm)
- OpenCV
- Gradio for web interface
- einops for tensor manipulation

## Model Details

The model weights are stored in `checkpoints/BEN2_Base.pth` and are managed with Git LFS due to their large size (~1.1GB). The model uses half-precision (float16) for inference to improve performance.

## Processing Pipeline

1. Images are resized to 1024x1024 for processing
2. Input is split into local patches and a global view
3. Swin Transformer extracts multi-scale features
4. Attention mechanisms fuse local and global information
5. Decoder refines the segmentation mask
6. Post-processing scales the mask back to original resolution
7. Alpha channel is applied to create transparent background

## Video Processing

Video processing is implemented by processing each frame individually and then combining the results into a new video. Audio can be preserved from the original video.