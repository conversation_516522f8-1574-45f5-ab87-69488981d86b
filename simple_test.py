#!/usr/bin/env python3
"""
Simple test for BEN2 MCP Server
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test if we can import the MCP server modules"""
    try:
        print("Testing imports...")
        
        # Test basic imports
        import torch
        print("✅ PyTorch imported")
        
        import mcp
        print("✅ MCP imported")
        
        from mcp.server.fastmcp import FastMCP
        print("✅ FastMCP imported")
        
        # Test BEN2 imports
        from ben_base import BEN_Base
        print("✅ BEN_Base imported")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_model_info():
    """Test the get_model_info function"""
    try:
        print("\nTesting get_model_info function...")
        
        # Import the function
        from mcp_server import get_model_info
        
        # Call the function
        info = get_model_info()
        print("✅ get_model_info called successfully")
        print(f"Model info: {info}")
        
        return True
        
    except Exception as e:
        print(f"❌ get_model_info test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Simple BEN2 MCP Test")
    print("=" * 40)
    
    success = True
    
    # Test imports
    if not test_import():
        success = False
    
    # Test model info
    if not test_model_info():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
