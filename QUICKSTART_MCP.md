# BEN2 MCP 快速开始指南

## 🚀 5分钟快速设置

### 1. 安装和配置

```bash
# 1. 安装依赖并配置
python install_mcp.py

# 2. 测试安装
python test_mcp_server.py

# 3. 启动MCP服务器
python start_mcp_server.py
```

### 2. 配置Claude Desktop

如果安装脚本没有自动配置，请手动添加以下配置到Claude Desktop：

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "ben2-background-removal": {
      "command": "python",
      "args": ["E:/aibox/ai/BEN2/BEN2/mcp_server.py"],
      "cwd": "E:/aibox/ai/BEN2/BEN2"
    }
  }
}
```

**重要**: 将路径替换为您的实际项目路径！

### 3. 重启Claude Desktop

配置完成后，重启Claude Desktop以加载MCP服务器。

## 🎯 使用示例

### 图像背景移除

```
用户: 请帮我移除这张图片的背景：image.jpg

Claude: 我来帮您移除图片背景...
[调用 remove_background_image 工具]
已成功移除背景！处理后的图片已保存。
```

### 视频背景移除

```
用户: 请处理这个视频文件，移除背景：video.mp4

Claude: 我来处理这个视频的背景移除...
[调用 remove_background_video 工具]
视频背景移除完成！输出文件：foreground.mp4
```

### 获取模型信息

```
用户: BEN2模型支持哪些功能？

Claude: 让我查看BEN2模型的详细信息...
[调用 get_model_info 工具]
BEN2模型支持以下功能：
- 图像背景移除
- 视频背景移除
- 支持多种图像格式...
```

## 🔧 常见问题

### Q: MCP服务器启动失败
**A**: 检查以下项目：
1. 确保所有依赖已安装：`pip install -r requirements.txt`
2. 确保模型文件存在：`checkpoints/BEN2_Base.pth`
3. 查看错误日志获取详细信息

### Q: Claude无法找到BEN2工具
**A**: 检查以下配置：
1. Claude Desktop配置文件路径是否正确
2. 项目路径是否正确（使用绝对路径）
3. 重启Claude Desktop
4. 确保MCP服务器正在运行

### Q: 图像处理失败
**A**: 可能的原因：
1. 图像文件不存在或路径错误
2. 图像格式不支持
3. 内存不足（尝试较小的图像）

### Q: 视频处理很慢
**A**: 这是正常的，因为：
1. 视频处理限制为前100帧
2. 如果没有GPU，处理会较慢
3. 可以尝试较短的视频文件

## 📁 文件结构

```
BEN2/
├── app.py                 # Gradio Web应用
├── ben_base.py           # BEN2模型核心
├── mcp_server.py         # MCP服务器
├── start_mcp_server.py   # 启动脚本
├── test_mcp_server.py    # 测试脚本
├── install_mcp.py        # 安装脚本
├── requirements.txt      # 依赖列表
├── README_MCP.md         # 详细说明
└── QUICKSTART_MCP.md     # 本文件
```

## 🎉 开始使用

现在您可以在Claude中直接使用BEN2的背景移除功能了！

尝试发送以下消息给Claude：
- "请帮我移除这张图片的背景"
- "处理这个视频，移除背景"
- "BEN2支持哪些图像格式？"

享受AI助手与专业背景移除工具的完美结合！
