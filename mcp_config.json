{"mcpServers": {"ben2-background-removal": {"command": "E:\\aibox\\ai\\BEN2\\BEN2\\py311\\python.exe", "args": ["mcp_server.py"], "cwd": "E:\\aibox\\ai\\BEN2\\BEN2", "env": {"PYTHONHOME": "", "PYTHONPATH": "", "PYTHON_EXECUTABLE": "E:\\aibox\\ai\\BEN2\\BEN2\\py311\\python.exe", "PYTHON_LIB_PATH": "E:\\aibox\\ai\\BEN2\\BEN2\\py311\\Lib\\site-packages", "CU_PATH": "E:\\aibox\\ai\\BEN2\\BEN2\\py311\\Lib\\site-packages\\torch\\lib", "cuda_PATH": "E:\\aibox\\ai\\BEN2\\BEN2\\py311\\Library\\bin", "FFMPEG_PATH": "E:\\aibox\\ai\\BEN2\\BEN2\\py311\\ffmpeg\\bin", "HF_ENDPOINT": "https://hf-mirror.com", "HF_HOME": "E:\\aibox\\ai\\BEN2\\BEN2\\checkpoints", "TRANSFORMERS_CACHE": "E:\\aibox\\ai\\BEN2\\BEN2\\checkpoints", "XFORMERS_FORCE_DISABLE_TRITON": "1", "PATH": "E:\\aibox\\ai\\BEN2\\BEN2\\py311;E:\\aibox\\ai\\BEN2\\BEN2\\py311\\Scripts;E:\\aibox\\ai\\BEN2\\BEN2\\py311\\ffmpeg\\bin;E:\\aibox\\ai\\BEN2\\BEN2\\py311\\Lib\\site-packages\\torch\\lib;E:\\aibox\\ai\\BEN2\\BEN2\\py311\\Library\\bin"}}}}