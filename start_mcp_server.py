#!/usr/bin/env python3
"""
BEN2 MCP Server Launcher
Starts the MCP server with proper error handling and logging
"""

import sys
import os
import subprocess
import logging

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = ['mcp', 'fastmcp', 'torch', 'gradio', 'PIL']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_model_files():
    """Check if model files exist"""
    model_path = "checkpoints/BEN2_Base.pth"
    if not os.path.exists(model_path):
        print(f"❌ Model file not found: {model_path}")
        print("Please ensure the model file is in the correct location.")
        return False
    
    print(f"✅ Model file found: {model_path}")
    return True

def main():
    """Main launcher function"""
    print("🚀 Starting BEN2 MCP Server...")
    print("=" * 50)
    
    # Check dependencies
    print("📦 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ All dependencies are installed")
    
    # Check model files
    print("🔍 Checking model files...")
    if not check_model_files():
        sys.exit(1)
    
    # Start the MCP server
    print("🌟 Starting MCP server...")
    print("Server will be available for MCP clients")
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Run the MCP server
        subprocess.run([sys.executable, "mcp_server.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed with exit code {e.returncode}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
