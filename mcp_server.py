#!/usr/bin/env python3
"""
BEN2 MCP Server - Model Context Protocol server for background removal
Provides AI models with access to BEN2's background removal capabilities
"""

import asyncio
import base64
import io
import os
import sys
import tempfile
from typing import Any, Dict, List, Optional
import logging

# Check and install MCP dependencies if needed
def check_and_install_mcp():
    """Check if MCP is available and try to install if not"""
    try:
        import mcp
        from mcp.server.fastmcp import FastMCP
        return True
    except ImportError:
        print("MCP not found, attempting to install...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp", "fastmcp", "uvicorn"])
            import mcp
            from mcp.server.fastmcp import FastMCP
            print("✅ MCP installed successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to install MCP: {e}")
            print("Please install manually: pip install mcp fastmcp uvicorn")
            return False

# Try to import MCP
if not check_and_install_mcp():
    sys.exit(1)

# MCP imports
from mcp.server.fastmcp import FastMCP

# BEN2 imports
import torch
import numpy as np
from PIL import Image
from loadimg import load_img
from ben_base import BEN_Base

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the MCP server
mcp = FastMCP("BEN2 Background Removal")

# Global model instance
model = None
device = None

def initialize_model():
    """Initialize the BEN2 model"""
    global model, device
    
    try:
        # Set random seed for reproducibility
        import random
        random.seed(9)
        np.random.seed(9)
        torch.manual_seed(9)
        torch.cuda.manual_seed(9)
        torch.cuda.manual_seed_all(9)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        
        torch.set_float32_matmul_precision("high")
        
        # Initialize model
        model = BEN_Base()
        model_path = "checkpoints/BEN2_Base.pth"
        
        # Check device
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {device}")
        
        # Check if model file exists
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        # Load model
        model.loadcheckpoints(model_path)
        model.to(device)
        model.eval()
        
        logger.info("BEN2 model initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize model: {e}")
        return False

def image_to_base64(image: Image.Image, format: str = "PNG") -> str:
    """Convert PIL Image to base64 string"""
    buffer = io.BytesIO()
    image.save(buffer, format=format)
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def base64_to_image(base64_str: str) -> Image.Image:
    """Convert base64 string to PIL Image"""
    img_data = base64.b64decode(base64_str)
    image = Image.open(io.BytesIO(img_data))
    return image

@mcp.tool()
def remove_background_image(
    image_input: str,
    input_type: str = "path",
    output_format: str = "base64"
) -> Dict[str, Any]:
    """
    Remove background from an image using BEN2 model.
    
    Args:
        image_input: Image input - either file path or base64 encoded image
        input_type: Type of input - "path" or "base64" (default: "path")
        output_format: Output format - "base64", "path", or "both" (default: "base64")
    
    Returns:
        Dictionary containing the processed image and metadata
    """
    try:
        if model is None:
            return {
                "success": False,
                "error": "Model not initialized. Please restart the server."
            }
        
        # Load image based on input type
        if input_type == "path":
            if not os.path.exists(image_input):
                return {
                    "success": False,
                    "error": f"Image file not found: {image_input}"
                }
            image = load_img(image_input, output_type="pil")
        elif input_type == "base64":
            image = base64_to_image(image_input)
        else:
            return {
                "success": False,
                "error": "Invalid input_type. Use 'path' or 'base64'"
            }
        
        # Convert to RGB if needed
        image = image.convert("RGB")
        
        # Process image
        logger.info("Processing image for background removal...")
        result_image = model.inference(image)
        
        # Prepare output
        result = {
            "success": True,
            "message": "Background removed successfully"
        }
        
        if output_format in ["base64", "both"]:
            result["image_base64"] = image_to_base64(result_image)
        
        if output_format in ["path", "both"]:
            # Save to temporary file
            output_folder = 'output_images'
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
            
            output_path = os.path.join(output_folder, f"result_{int(asyncio.get_event_loop().time())}.png")
            result_image.save(output_path)
            result["image_path"] = output_path
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        return {
            "success": False,
            "error": f"Failed to process image: {str(e)}"
        }

@mcp.tool()
def remove_background_video(
    video_path: str,
    output_path: Optional[str] = None
) -> Dict[str, Any]:
    """
    Remove background from a video using BEN2 model.
    
    Args:
        video_path: Path to the input video file
        output_path: Path for the output video (optional, defaults to ./foreground.mp4)
    
    Returns:
        Dictionary containing the processed video path and metadata
    """
    try:
        if model is None:
            return {
                "success": False,
                "error": "Model not initialized. Please restart the server."
            }
        
        # Check if input video exists
        if not os.path.exists(video_path):
            return {
                "success": False,
                "error": f"Video file not found: {video_path}"
            }
        
        # Set default output path if not provided
        if output_path is None:
            output_path = "./foreground.mp4"
        
        # Process video
        logger.info(f"Processing video: {video_path}")
        model.segment_video(video_path)  # This saves to ./foreground.mp4
        
        # Check if output was created
        if os.path.exists("./foreground.mp4"):
            # Move to desired output path if different
            if output_path != "./foreground.mp4":
                import shutil
                shutil.move("./foreground.mp4", output_path)
            
            return {
                "success": True,
                "message": "Video background removed successfully",
                "output_path": output_path,
                "note": "Video processing is limited to the first 100 frames for performance reasons"
            }
        else:
            return {
                "success": False,
                "error": "Video processing failed - output file not created"
            }
        
    except Exception as e:
        logger.error(f"Error processing video: {e}")
        return {
            "success": False,
            "error": f"Failed to process video: {str(e)}"
        }

@mcp.tool()
def get_model_info() -> Dict[str, Any]:
    """
    Get information about the BEN2 model and server status.
    
    Returns:
        Dictionary containing model and server information
    """
    return {
        "model_name": "BEN2",
        "model_type": "Background Removal",
        "device": str(device) if device else "Not initialized",
        "model_loaded": model is not None,
        "supported_formats": {
            "images": ["PNG", "JPEG", "JPG", "BMP", "TIFF"],
            "videos": ["MP4", "AVI", "MOV", "MKV"]
        },
        "capabilities": [
            "Single image background removal",
            "Video background removal (first 100 frames)",
            "Base64 and file path input support",
            "Multiple output formats"
        ]
    }

def main():
    """Main function to run the MCP server"""
    # Initialize the model
    logger.info("Initializing BEN2 model...")
    if not initialize_model():
        logger.error("Failed to initialize model. Exiting.")
        sys.exit(1)

    # Run the MCP server
    logger.info("Starting BEN2 MCP Server...")
    logger.info("Available tools:")
    logger.info("- remove_background_image: Remove background from images")
    logger.info("- remove_background_video: Remove background from videos")
    logger.info("- get_model_info: Get model and server information")

    # Use the synchronous run method
    mcp.run()

if __name__ == "__main__":
    main()
