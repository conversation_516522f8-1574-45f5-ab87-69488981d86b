# BEN2 MCP Server PowerShell启动脚本
# 使用本地Python环境启动MCP服务器

Write-Host "🚀 Starting BEN2 MCP Server..." -ForegroundColor Green
Write-Host "================================" -ForegroundColor Yellow

# 设置Python路径（使用绝对路径）
$PYTHON_PATH = "E:\aibox\ai\BEN2\BEN2\py311"
$PYTHON_EXE = "$PYTHON_PATH\python.exe"

Write-Host "Python path: $PYTHON_PATH" -ForegroundColor Cyan
Write-Host "Python executable: $PYTHON_EXE" -ForegroundColor Cyan

# 配置环境变量
$env:PYTHONHOME = ""
$env:PYTHONPATH = ""
$env:PYTHONEXECUTABLE = $PYTHON_EXE
$env:PYTHONWEXECUTABLE = "$PYTHON_PATH\pythonw.exe"
$env:PYTHON_EXECUTABLE = $PYTHON_EXE
$env:PYTHONW_EXECUTABLE = "$PYTHON_PATH\pythonw.exe"
$env:PYTHON_BIN_PATH = $PYTHON_EXE
$env:PYTHON_LIB_PATH = "$PYTHON_PATH\Lib\site-packages"
$env:CU_PATH = "$PYTHON_PATH\Lib\site-packages\torch\lib"
$env:cuda_PATH = "$PYTHON_PATH\Library\bin"
$env:FFMPEG_PATH = "E:\aibox\ai\BEN2\BEN2\py311\ffmpeg\bin"
$env:PATH = "$PYTHON_PATH;$PYTHON_PATH\Scripts;$env:FFMPEG_PATH;$env:CU_PATH;$env:cuda_PATH;$env:PATH"
$env:HF_ENDPOINT = "https://hf-mirror.com"
$env:HF_HOME = "E:\aibox\ai\BEN2\BEN2\checkpoints"
$env:TRANSFORMERS_CACHE = "E:\aibox\ai\BEN2\BEN2\checkpoints"
$env:XFORMERS_FORCE_DISABLE_TRITON = "1"

# 检查Python是否可用
Write-Host "📦 Checking Python environment..." -ForegroundColor Cyan
if (Test-Path $PYTHON_EXE) {
    Write-Host "✅ Python found: $PYTHON_EXE" -ForegroundColor Green
    & $PYTHON_EXE --version
} else {
    Write-Host "❌ Python not found at: $PYTHON_EXE" -ForegroundColor Red
    Write-Host "Please ensure the py311 directory exists and contains Python." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# 检查必要文件
Write-Host "🔍 Checking required files..." -ForegroundColor Cyan

$required_files = @(
    "mcp_server.py",
    "ben_base.py",
    "checkpoints\BEN2_Base.pth"
)

$missing_files = @()
foreach ($file in $required_files) {
    if (Test-Path $file) {
        Write-Host "✅ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $file" -ForegroundColor Red
        $missing_files += $file
    }
}

if ($missing_files.Count -gt 0) {
    Write-Host "❌ Missing required files. Please ensure all files are present." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 安装MCP依赖
Write-Host "📦 Installing MCP dependencies..." -ForegroundColor Cyan
try {
    & $PYTHON_EXE -m pip install mcp fastmcp uvicorn --quiet
    Write-Host "✅ MCP dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Failed to install some dependencies, continuing..." -ForegroundColor Yellow
}

# 启动MCP服务器
Write-Host "🌟 Starting MCP server..." -ForegroundColor Cyan
Write-Host "Server will be available for MCP clients" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

try {
    & $PYTHON_EXE mcp_server.py
} catch {
    Write-Host "❌ Failed to start MCP server: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "👋 MCP Server stopped" -ForegroundColor Yellow
Read-Host "Press Enter to exit"
